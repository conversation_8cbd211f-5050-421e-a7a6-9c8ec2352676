<script lang="ts">
	import { onMount, afterUpdate, createEventDispatcher } from 'svelte';
	import { t, language } from '$lib/stores/i18n';

	import MessageItem from './MessageItem.svelte';
	import LoadingSpinner from '../common/LoadingSpinner.svelte';
	import InfiniteScroll from '../common/InfiniteScroll.svelte';

	import type { Message } from '$lib/types/customer';
	import { formatMessageDate } from '$lib/utils/messageFormatter';

	import { Badge } from 'flowbite-svelte';
	import { TicketSolid } from 'flowbite-svelte-icons';

	// Configuration: Number of messages to load per request
	const MESSAGES_PER_LOAD = 1;

	export let messages: Message[] = [];
	export let loading: boolean = false;
	export let hasMore: boolean = true;
	export let focusedTicketId: number | null = null;
	
	const dispatch = createEventDispatcher();

	let scrollContainer: HTMLElement;
	let shouldScrollToBottom = false; // Start as false to prevent unwanted auto-scrolling on init
	let isNearBottom = true;

	// Enhanced auto-scroll state
	let isInitialLoad = true;
	let previousMessageCount = 0;

	// Sticky header state
	let stickyDate: string = '';
	let stickyTicketId: number | null = null;
	let showStickyHeader = false;
	let messageGroupElementsByTicket: HTMLElement[] = [];
	let dateGroupElements: HTMLElement[] = [];

	// Ticket navigation state
	let ticketElements: Map<number, HTMLElement> = new Map();
	let isNavigating = false;
	let scrollInProgress = false;

	onMount(() => {
		// Listen for ticket navigation events
		if (typeof window !== 'undefined') {
			window.addEventListener('navigate-to-ticket', handleNavigateToTicket);
		}
		
		// Cleanup on destroy
		return () => {
			if (typeof window !== 'undefined') {
				window.removeEventListener('navigate-to-ticket', handleNavigateToTicket);
			}
		};
	});

	// Simplified scroll manager - system scrolling has priority
	async function performScroll(action: 'ticket-end' | 'bottom', ticketId?: number) {
		// If a scroll is already in progress, wait for it to complete
		if (scrollInProgress) {
			return;
		}
		
		scrollInProgress = true;
		
		try {
			if (action === 'ticket-end' && ticketId) {
				await scrollToTicketEnd(ticketId);
			} else if (action === 'bottom') {
				await executeBottomScroll();
			}
		} finally {
			scrollInProgress = false;
		}
	}

	async function scrollToTicketEnd(ticketId: number): Promise<void> {
		const ticketElement = ticketElements.get(ticketId);
		
		if (ticketElement && scrollContainer) {
			isNavigating = true;
			shouldScrollToBottom = false;
			
			// Set focused ticket for visual highlighting
			focusedTicketId = ticketId;
			
			// Scroll to the end of the specific ticket with proper spacing
			const targetScrollTop = Math.max(0, 
				ticketElement.offsetTop + ticketElement.offsetHeight - scrollContainer.clientHeight + 100
			);
			
			scrollContainer.scrollTo({
				top: targetScrollTop,
				behavior: 'smooth'
			});
			
			// Wait for scroll animation to complete
			await new Promise(resolve => {
				setTimeout(() => {
					isNavigating = false;
					// Use enhanced bottom detection for consistency
					shouldScrollToBottom = isAtBottom(10);
					resolve(void 0);
				}, 1000);
			});
		}
	}

	// Wait for all images in the scroll container to load
	async function waitForImages(): Promise<void> {
		if (!scrollContainer) return;

		const images = scrollContainer.querySelectorAll('img');
		if (images.length === 0) return;

		const imagePromises = Array.from(images).map(img => {
			if (img.complete) return Promise.resolve();
			return new Promise<void>(resolve => {
				img.onload = () => resolve();
				img.onerror = () => resolve(); // Still resolve on error to not block
				// Timeout fallback in case image never loads
				setTimeout(() => resolve(), 2000);
			});
		});

		await Promise.all(imagePromises);
	}

	// Wait for all content to be fully rendered and sized
	async function waitForAllContent(): Promise<void> {
		if (!scrollContainer) return;

		// Wait for images to load first
		await waitForImages();

		// Wait for DOM updates to complete using multiple animation frames
		// This ensures all dynamic content has been rendered and sized
		await new Promise<void>(resolve => {
			requestAnimationFrame(() => {
				requestAnimationFrame(() => {
					requestAnimationFrame(() => {
						resolve();
					});
				});
			});
		});

		// Additional small delay to account for any remaining layout calculations
		await new Promise<void>(resolve => setTimeout(resolve, 50));
	}

	// Enhanced bottom detection with multiple verification methods
	function isAtBottom(tolerance: number = 10): boolean {
		if (!scrollContainer) return false;

		const { scrollTop, scrollHeight, clientHeight } = scrollContainer;

		// Method 1: Standard calculation
		const distanceFromBottom = scrollHeight - scrollTop - clientHeight;
		const method1 = distanceFromBottom <= tolerance;

		// Method 2: Direct position check
		const method2 = scrollTop >= (scrollHeight - clientHeight - tolerance);

		// Method 3: Percentage-based check (within 99.5% of total scroll)
		const scrollPercentage = scrollTop / (scrollHeight - clientHeight);
		const method3 = scrollPercentage >= 0.995;

		// Return true if any method confirms we're at bottom
		return method1 || method2 || method3;
	}

	async function executeBottomScroll(): Promise<void> {
		if (!scrollContainer) return;

		// Wait for all content to be fully rendered
		await waitForAllContent();

		return new Promise(resolve => {
			const attemptScroll = (attempt = 0) => {
				requestAnimationFrame(() => {
					const { scrollHeight, clientHeight } = scrollContainer;

					// Use scrollTo with behavior 'auto' to avoid conflicts with CSS smooth scrolling
					const targetScrollTop = scrollHeight - clientHeight;
					scrollContainer.scrollTo({
						top: targetScrollTop,
						behavior: 'auto'
					});

					// Verify we're actually at the bottom after a delay
					const verificationDelay = Math.min(100 + (attempt * 50), 300); // Increasing delay with attempts
					setTimeout(() => {
						if (isAtBottom(10)) {
							// Successfully at bottom
							resolve(void 0);
						} else if (attempt < 5) {
							// Retry up to 5 times with exponential backoff
							const retryDelay = Math.pow(2, attempt) * 25; // 25ms, 50ms, 100ms, 200ms, 400ms
							setTimeout(() => attemptScroll(attempt + 1), retryDelay);
						} else {
							// Final attempt: Force scroll to maximum possible position
							const maxScrollTop = Math.max(0, scrollHeight - clientHeight);
							scrollContainer.scrollTo({
								top: maxScrollTop + 50, // Add extra pixels to ensure we're past the bottom
								behavior: 'auto'
							});

							// Final verification after force scroll
							setTimeout(() => {
								resolve(void 0);
							}, 100);
						}
					}, verificationDelay);
				});
			};

			attemptScroll();
		});
	}

	afterUpdate(() => {
		const hasNewMessages = messages.length > previousMessageCount;

		if (isInitialLoad && messages.length > 0) {
			// Initial load - always scroll to bottom
			performScroll('bottom');
			isInitialLoad = false;
		} else if (hasNewMessages && isNearBottom && !isNavigating && focusedTicketId === null) {
			// New messages arrived and user is at/near bottom - auto-scroll
			performScroll('bottom');
		}

		// Update message count for next comparison
		previousMessageCount = messages.length;
	});



	// Handle ticket selection from left panel - always scroll to ticket end
	function handleNavigateToTicket(event: CustomEvent) {
		const { ticketId } = event.detail;
		shouldScrollToBottom = false;
		
		// Always scroll to the end of the selected ticket
		performScroll('ticket-end', ticketId);
	}



	function handleScroll(event: Event) {
		if (!scrollContainer) return;

		// Prevent manual scrolling when system is scrolling
		if (scrollInProgress) {
			event.preventDefault();
			return;
		}

		const { scrollTop, scrollHeight, clientHeight } = scrollContainer;
		const distanceFromBottom = scrollHeight - scrollTop - clientHeight;

		// Check if user is near bottom (within 100px) - keep existing threshold for compatibility
		isNearBottom = distanceFromBottom < 100;

		// Check if scrolled to top for loading more
		if (scrollTop === 0 && messages.length > 0 && hasMore) {
			dispatch('loadMore', {
				limit: MESSAGES_PER_LOAD
			});
		}

		// Update sticky header based on scroll position
		updateStickyHeader();
	}

	function updateStickyHeader() {
		if (!scrollContainer || dateGroupElements.length === 0) return;

		const scrollTop = scrollContainer.scrollTop;
		const containerTop = scrollContainer.getBoundingClientRect().top;

		// Find the topmost visible date group
		let currentDateGroup = null;
		let currentTicketId = null;

		for (let i = 0; i < dateGroupElements.length; i++) {
			const element = dateGroupElements[i];
			if (!element) continue;

			const rect = element.getBoundingClientRect();
			const elementTop = rect.top - containerTop;

			// If this date group is visible or partially visible at the top
			if (elementTop <= 20) {
				// 20px threshold for sticky header activation
				const allDateGroups = getAllDateGroups();
				currentDateGroup = allDateGroups[i];
				// Find the ticket ID for this date group
				for (const ticketGroup of messageGroupedByTicketAndDate) {
					if (ticketGroup.dateGroups.includes(currentDateGroup)) {
						currentTicketId = ticketGroup.ticketId;
						break;
					}
				}
			} else {
				break;
			}
		}

		if (currentDateGroup && scrollTop > 50) {
			// Only show sticky header after scrolling 50px
			showStickyHeader = true;
			stickyDate = formatMessageDate(currentDateGroup.messages[0].created_on);
			stickyTicketId = currentTicketId;
		} else {
			showStickyHeader = false;
		}
	}

	// Helper function to get all date groups in a flat array for tracking
	function getAllDateGroups() {
		const allDateGroups: { messages: Message[] }[] = [];
		messageGroupedByTicketAndDate.forEach((ticketGroup) => {
			ticketGroup.dateGroups.forEach((dateGroup) => {
				allDateGroups.push(dateGroup);
			});
		});
		return allDateGroups;
	}

	// Helper function to calculate global date group index
	function getGlobalDateGroupIndex(ticketGroupIndex: number, dateGroupIndex: number): number {
		let globalIndex = 0;
		for (let i = 0; i < ticketGroupIndex; i++) {
			globalIndex += messageGroupedByTicketAndDate[i].dateGroups.length;
		}
		return globalIndex + dateGroupIndex;
	}

	// Group messages by ticket, then by date within each ticket
	function groupMessagesByTicketAndDate(messages: Message[]) {
		const groups: {
			ticketId: number;
			dateGroups: { date: string; messages: Message[] }[];
		}[] = [];
		let currentTicketId = -1;

		messages.forEach((msg) => {
			const msgDate = new Date(msg.created_on).toLocaleDateString();

			if (msg.ticket_id !== currentTicketId) {
				// New ticket group
				currentTicketId = msg.ticket_id;
				groups.push({
					ticketId: currentTicketId,
					dateGroups: [
						{
							date: msgDate,
							messages: [msg]
						}
					]
				});
				// For Debugging
				// console.log('MessageList.svelte: groupMessagesByTicketAndDate(): New ticket group created:', groups[groups.length - 1]);
			} else {
				// Same ticket, check if we need a new date group
				const currentTicketGroup = groups[groups.length - 1];
				const lastDateGroup =
					currentTicketGroup.dateGroups[currentTicketGroup.dateGroups.length - 1];

				if (lastDateGroup.date !== msgDate) {
					// New date within the same ticket
					currentTicketGroup.dateGroups.push({
						date: msgDate,
						messages: [msg]
					});
				} else {
					// Same date, add to existing date group
					lastDateGroup.messages.push(msg);
				}
			}
		});
		return groups;
	}

	// Check if should show avatar (first message or different sender)
	function shouldShowAvatar(message: Message, index: number, messages: Message[]) {
		if (index === 0) return true;
		const prevMessage = messages[index - 1];
		return prevMessage.is_self !== message.is_self || prevMessage.user_name !== message.user_name;
	}

	$: messageGroupedByTicketAndDate = groupMessagesByTicketAndDate(messages);

	// Calculate total number of date groups across all tickets
	$: totalDateGroups = messageGroupedByTicketAndDate.reduce((total, ticketGroup) => {
		return total + ticketGroup.dateGroups.length;
	}, 0);

	// Ensure messageGroupElementsByTicket array is properly sized for the new nested structure
	$: if (messageGroupedByTicketAndDate.length !== messageGroupElementsByTicket.length) {
		messageGroupElementsByTicket = new Array(messageGroupedByTicketAndDate.length);
	}

	// Ensure dateGroupElements array is properly sized
	$: if (totalDateGroups !== dateGroupElements.length) {
		dateGroupElements = new Array(totalDateGroups);
	}

	// Update ticketElements map when messageGroupElementsByTicket changes
	$: if (messageGroupElementsByTicket && messageGroupedByTicketAndDate.length > 0) {
		ticketElements.clear();
		messageGroupedByTicketAndDate.forEach((ticketGroup, index) => {
			if (messageGroupElementsByTicket[index]) {
				ticketElements.set(ticketGroup.ticketId, messageGroupElementsByTicket[index]);
			}
		});
	}
</script>

<div
	id="message-list-container"
	bind:this={scrollContainer}
	on:scroll={handleScroll}
	class="custom-scrollbar relative flex-1 overflow-y-auto bg-gray-50 px-6 py-4 {scrollInProgress ? 'disable-scroll' : ''}"
>
	<!-- Sticky Header -->
	{#if showStickyHeader}
		<div id="message-list-sticky-header" class="sticky top-0 z-10 mb-4 pb-2 transition-all duration-200">
			<div class="my-4 flex items-center justify-center">
				<span
					id="message-list-sticky-date"
					class="flex items-center justify-center rounded-full bg-gray-900 bg-opacity-40 px-3 py-1 text-center text-xs text-white"
				>
					{#if stickyTicketId}
						<TicketSolid class="mr-2 h-4 w-4" />
						#{stickyTicketId} • 
					{/if}
					{stickyDate}
				</span>
			</div>
		</div>
	{/if}
	{#if loading && messages.length === 0}
		<div id="message-list-initial-loading" class="flex h-full items-center justify-center">
			<LoadingSpinner />
		</div>
	{:else}
		<!-- Load more indicator at top -->
		{#if loading && messages.length > 0}
			<div id="message-list-load-more-indicator" class="flex justify-center py-2">
				<LoadingSpinner size="sm" />
			</div>
		{/if}

		<!-- Messages grouped by ticket and date -->
		{#each messageGroupedByTicketAndDate as ticketGroup, ticketGroupIndex}
			<div 
				bind:this={messageGroupElementsByTicket[ticketGroupIndex]} 
				class="message-group {focusedTicketId === ticketGroup.ticketId ? 'focused-ticket' : ''}"
				id="message-list-ticket-group-{ticketGroup.ticketId}" 
			>
				<!-- Ticket separator -->
				<div id="message-list-ticket-separator-{ticketGroup.ticketId}" class="my-10 my-4 flex items-center justify-center">
					<span
						class="flex items-center justify-center rounded-full transition-all duration-500
						       {focusedTicketId === ticketGroup.ticketId 
						           ? 'bg-blue-600 px-4 py-2 text-sm shadow-lg border-2 border-blue-300' 
						           : 'bg-gray-900 bg-opacity-40 px-3 py-1 text-xs'} 
						       text-white"
						id="message-list-ticket-badge-{ticketGroup.ticketId}"	   
					>
						<TicketSolid class="mr-2 h-5 w-5" />
						{ticketGroup.ticketId}
					</span>
				</div>

				<!-- Date groups within ticket -->
				{#each ticketGroup.dateGroups as dateGroup, dateGroupIndex}
					{@const globalDateGroupIndex = getGlobalDateGroupIndex(ticketGroupIndex, dateGroupIndex)}
					<!-- Date separator -->
					<div
						id="message-list-date-separator-{dateGroup.date.replace(/\//g, '-')}"
						bind:this={dateGroupElements[globalDateGroupIndex]}
						class="my-4 flex items-center justify-center"
					>
						<span
							id="message-list-date-badge-{dateGroup.date.replace(/\//g, '-')}"
							class="flex items-center justify-center rounded-full bg-gray-900 bg-opacity-40 px-3 py-1 text-center text-xs text-white"
						>
							{formatMessageDate(dateGroup.messages[0].created_on)}
						</span>
					</div>

					<!-- Messages in date group -->
					<div id="message-list-messages-{dateGroup.date.replace(/\//g, '-')}">
						{#each dateGroup.messages as message, messageIndex}
							<MessageItem
								{message}
								showAvatar={shouldShowAvatar(message, messageIndex, dateGroup.messages)}
							/>
						{/each}
					</div>
				{/each}
			</div>
		{/each}

		{#if messages.length === 0}
			<div id="message-list-empty-state" class="mt-8 text-center text-gray-500">
				{t('no_messages')}
			</div>
		{/if}
	{/if}
</div>

<style>
	/* Smooth scroll behavior */
	.custom-scrollbar {
		scroll-behavior: smooth;
	}

	/* Disable scrolling when system is scrolling */
	.disable-scroll {
		pointer-events: none;
		user-select: none;
	}

	/* Focused ticket styling */
	.focused-ticket {
		border-left: 4px solid #3b82f6;
		padding-left: 1.5rem;
		margin-left: -1.5rem;
		border-radius: 0.5rem;
	}
</style>
